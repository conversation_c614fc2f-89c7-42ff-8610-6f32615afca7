import '@testing-library/jest-dom';
import { beforeAll, afterEach, afterAll, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import { setupServer } from 'msw/node';
import { handlers } from './mocks/handlers';

// MSW server setup
export const server = setupServer(...handlers);

// Mock browser extension APIs - configurable for tests
if (typeof (globalThis as any).browser === 'undefined') {
  Object.defineProperty(globalThis, 'browser', {
    value: {
      storage: {
        local: {
          get: vi.fn().mockImplementation((key) => {
            // Always return proper config structure for translate config
            const result = {
              'lucid-translate-config': {
                engines: {
                  google: { enabled: true, priority: 1 },
                  microsoft: { enabled: true, priority: 2 }
                }
              }
            };
            return Promise.resolve(result);
          }),
          set: vi.fn().mockResolvedValue(undefined),
          remove: vi.fn().mockResolvedValue(undefined),
          clear: vi.fn().mockResolvedValue(undefined)
        }
      },
      runtime: {
        getURL: vi.fn((path: string) => `chrome-extension://test-id/${path}`),
        sendMessage: vi.fn().mockResolvedValue({ success: true, data: 'mock response' }),
        id: 'test-extension-id',
        lastError: null
      }
    },
    configurable: true,
    writable: true
  });
}

// Mock chrome API (fallback for browser API)
Object.defineProperty(global, 'chrome', {
  value: (globalThis as any).browser,
  configurable: true,
  writable: true
});

// Mock DOM APIs that might be missing in test environment
Object.defineProperty(global, 'ResizeObserver', {
  value: class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
});

// Setup and teardown
beforeAll(() => {
  server.listen({ onUnhandledRequest: 'error' });
});

afterEach(() => {
  cleanup();
  server.resetHandlers();
});

afterAll(() => {
  server.close();
});