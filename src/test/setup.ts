import '@testing-library/jest-dom';
import { beforeAll, afterEach, afterAll, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import { setupServer } from 'msw/node';
import { handlers } from './mocks/handlers';

// MSW server setup
export const server = setupServer(...handlers);

// Mock browser extension APIs - configurable for tests
if (typeof (globalThis as any).browser === 'undefined') {
  Object.defineProperty(globalThis, 'browser', {
    value: {
      storage: {
        local: {
          get: vi.fn().mockImplementation((key) => {
            // Return proper config structure based on the key requested
            if (key === 'lucid-translate-config' || (Array.isArray(key) && key.includes('lucid-translate-config'))) {
              return Promise.resolve({
                'lucid-translate-config': {
                  engines: {
                    google: {
                      enabled: true,
                      priority: 1,
                      apis: [
                        { name: 'google-translate-api-1', enabled: true, url: 'https://translate-pa.googleapis.com/v1/translateHtml' },
                        { name: 'google-translate-api-2', enabled: true, url: 'https://translate.googleapis.com/translate_a/t' }
                      ]
                    },
                    microsoft: {
                      enabled: true,
                      priority: 2,
                      apis: [
                        { name: 'azure-translator', enabled: true, url: 'https://api.cognitive.microsofttranslator.com/translate' }
                      ]
                    }
                  }
                }
              });
            }
            // For other keys, return empty result
            return Promise.resolve({});
          }),
          set: vi.fn().mockResolvedValue(undefined),
          remove: vi.fn().mockResolvedValue(undefined),
          clear: vi.fn().mockResolvedValue(undefined)
        }
      },
      runtime: {
        getURL: vi.fn((path: string) => `chrome-extension://test-id/${path}`),
        sendMessage: vi.fn().mockResolvedValue({ success: true, data: 'mock response' }),
        id: 'test-extension-id',
        lastError: null
      }
    },
    configurable: true,
    writable: true
  });
}

// Mock chrome API (fallback for browser API)
Object.defineProperty(global, 'chrome', {
  value: (globalThis as any).browser,
  configurable: true,
  writable: true
});

// Mock DOM APIs that might be missing in test environment
Object.defineProperty(global, 'ResizeObserver', {
  value: class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
});

// Mock CustomEvent for JSDOM compatibility
if (typeof CustomEvent === 'undefined') {
  global.CustomEvent = class CustomEvent extends Event {
    detail: any;

    constructor(type: string, options: CustomEventInit = {}) {
      super(type, options);
      this.detail = options.detail;
    }
  } as any;
}

// Mock fetch for tests that don't use MSW
if (typeof global.fetch === 'undefined') {
  global.fetch = vi.fn().mockResolvedValue({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
  });
}

// Setup and teardown
beforeAll(() => {
  server.listen({
    onUnhandledRequest: 'warn' // Change from 'error' to 'warn' to be less strict
  });
});

afterEach(() => {
  cleanup();
  server.resetHandlers();
  // Reset all mocks including browser API mocks
  vi.clearAllMocks();
});

afterAll(() => {
  server.close();
});