import { http, HttpResponse } from 'msw';

// Mock dictionary API responses
export const mockDictionaryData = {
  'hello': {
    word: 'hello',
    phonetic: '/həˈləʊ/',
    phonetics: [
      {
        text: '/həˈləʊ/',
        audio: 'https://api.dictionaryapi.dev/media/pronunciations/en/hello-us.mp3'
      }
    ],
    meanings: [
      {
        partOfSpeech: 'noun',
        definitions: [
          {
            definition: 'A greeting or expression of goodwill.',
            example: 'She gave me a warm hello.',
            synonyms: ['greeting', 'salutation']
          }
        ]
      },
      {
        partOfSpeech: 'verb',
        definitions: [
          {
            definition: 'To greet someone by saying hello.',
            example: 'I helloed to my neighbor.',
            synonyms: ['greet', 'salute']
          }
        ]
      }
    ]
  },
  'test': {
    word: 'test',
    phonetic: '/tɛst/',
    phonetics: [
      {
        text: '/tɛst/',
        audio: 'https://api.dictionaryapi.dev/media/pronunciations/en/test-us.mp3'
      }
    ],
    meanings: [
      {
        partOfSpeech: 'noun',
        definitions: [
          {
            definition: 'A procedure intended to establish the quality, performance, or reliability of something.',
            example: 'The test results were positive.',
            synonyms: ['examination', 'trial']
          }
        ]
      },
      {
        partOfSpeech: 'verb',
        definitions: [
          {
            definition: 'To take measures to check the quality, performance, or reliability of something.',
            example: 'We need to test the new software.',
            synonyms: ['examine', 'try']
          }
        ]
      }
    ]
  }
};

export const handlers = [
  // Dictionary API mock
  http.get('http://localhost:4000/api/dictionary/en/:word', ({ params }) => {
    const word = params.word as string;
    const mockData = mockDictionaryData[word as keyof typeof mockDictionaryData];

    if (mockData) {
      return HttpResponse.json(mockData);
    }

    return HttpResponse.json(
      { error: 'Word not found' },
      { status: 404 }
    );
  }),

  // Handle fresh parameter
  http.get('http://localhost:4000/api/dictionary/en/:word', ({ params, request }) => {
    const url = new URL(request.url);
    const fresh = url.searchParams.get('fresh');
    const word = params.word as string;

    // Simulate cache behavior
    if (fresh === 'true') {
      // Force fresh data
      console.log(`Mock API: Fetching fresh data for "${word}"`);
    }

    const mockData = mockDictionaryData[word as keyof typeof mockDictionaryData];

    if (mockData) {
      return HttpResponse.json(mockData);
    }

    return HttpResponse.json(
      { error: 'Word not found' },
      { status: 404 }
    );
  }),

  // Error simulation for testing
  http.get('http://localhost:4000/api/dictionary/en/error', () => {
    return HttpResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }),

  // Timeout simulation
  http.get('http://localhost:4000/api/dictionary/en/timeout', () => {
    return new Promise(() => {
      // Never resolve to simulate timeout
    });
  }),

  // Microsoft Translate API - Authentication endpoint
  http.get('https://edge.microsoft.com/translate/auth', () => {
    return HttpResponse.text('mock-microsoft-auth-token-12345');
  }),

  // Microsoft Translate API - Translation endpoint
  http.post('https://api.cognitive.microsofttranslator.com/translate', async ({ request }) => {
    const body = await request.json() as Array<{ Text: string }>;
    
    // Mock translation responses
    const translations = body.map(item => ({
      translations: [
        {
          text: getMockTranslation(item.Text),
          to: 'zh-Hans'
        }
      ]
    }));

    return HttpResponse.json(translations);
  }),

  // Google Translate API 1 - translateHtml endpoint (protobuf)
  http.post('https://translate-pa.googleapis.com/v1/translateHtml', async ({ request }) => {
    const body = await request.json() as [string[], string, string, string];
    const texts = body[0];
    
    // Mock protobuf response format
    const translations = texts.map(text => [
      getMockTranslation(text),
      text,
      null,
      null,
      1
    ]);

    return HttpResponse.json([
      [translations],
      'te_lib'
    ]);
  }),

  // Google Translate API 2 - translate_a/t endpoint (form data)
  http.post('https://translate.googleapis.com/translate_a/t', async ({ request }) => {
    const formData = await request.formData();
    const texts = formData.getAll('q') as string[];
    
    // Mock simplified response format
    const translations = texts.map(text => [
      getMockTranslation(text),
      text,
      null,
      null,
      1
    ]);

    return HttpResponse.text(JSON.stringify([translations]));
  }),

  // Fallback Google Translate endpoint
  http.get('https://translate.googleapis.com/translate_a/single', ({ request }) => {
    const url = new URL(request.url);
    const text = url.searchParams.get('q');
    
    if (!text) {
      return HttpResponse.json({ error: 'No text provided' }, { status: 400 });
    }

    const translation = getMockTranslation(text);
    return HttpResponse.text(JSON.stringify([[[translation, text, null, null, 1]]]));
  })
];

// Helper function to provide mock translations
function getMockTranslation(text: string): string {
  const translations: Record<string, string> = {
    'Hello, World!': '你好，世界！',
    'How are you today?': '你今天好吗？',
    'This is a test sentence.': '这是一个测试句子。',
    'Good morning, everyone!': '大家早上好！',
    'Hello': '你好',
    'World': '世界',
    'test': '测试',
    'Hello, World': '你好，世界'
  };

  return translations[text] || `翻译: ${text}`;
}