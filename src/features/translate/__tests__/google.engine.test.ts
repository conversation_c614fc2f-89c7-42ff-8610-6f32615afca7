/**
 * Google翻译引擎测试
 * 测试Google引擎的核心功能：API调用、分块处理、降级策略等
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { GoogleTranslateEngine } from '../engines/google';
import { TranslateErrorType, GoogleEngineConfig } from '../types';

// Use the global browser mocking from setup.ts - don't override it

describe('GoogleTranslateEngine', () => {
  let engine: GoogleTranslateEngine;
  let mockConfig: GoogleEngineConfig;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockConfig = {
      name: 'google',
      enabled: true,
      priority: 1,
      maxChunkSize: 5000,
      maxBatchSize: 128,
      timeout: 10000,
      retryCount: 2,
      apis: [
        {
          name: 'google-translate-api-1',
          url: 'https://translate.googleapis.com/translate_a/single',
          priority: 1,
          enabled: true,
          timeout: 8000
        },
        {
          name: 'google-translate-api-2',
          url: '',
          priority: 2,
          enabled: false,
          timeout: 8000
        }
      ]
    };
    
    engine = new GoogleTranslateEngine(mockConfig);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('基础配置', () => {
    it('应该正确设置引擎基础属性', () => {
      expect(engine.name).toBe('google');
      expect(engine.displayName).toBe('Google Translate');
      expect(engine.maxChunkSize).toBe(5000);
      expect(engine.maxBatchSize).toBe(128);
    });

    it('应该继承配置信息', () => {
      const config = engine.getGoogleConfig();
      expect(config.name).toBe('google');
      expect(config.enabled).toBe(true);
      expect(config.apis).toHaveLength(2);
    });
  });

  describe('支持的语言', () => {
    it('应该返回支持的语言列表', () => {
      const languages = engine.getSupportedLanguages();
      expect(languages).toContain('en');
      expect(languages).toContain('zh');
      expect(languages).toContain('auto');
      expect(languages.length).toBeGreaterThan(50);
    });

    it('应该验证语言代码', () => {
      const validOptions = engine['validateOptions']({ from: 'en', to: 'zh' });
      expect(validOptions.from).toBe('en');
      expect(validOptions.to).toBe('zh');
    });

    it('应该处理无效语言代码', () => {
      const validOptions = engine['validateOptions']({ from: 'invalid', to: 'zh' });
      expect(validOptions.from).toBe('auto');
      expect(validOptions.to).toBe('zh');
    });
  });

  describe('文本分块处理', () => {
    it('应该处理小量文本', () => {
      const texts = ['Hello', 'World'];
      const chunks = engine['chunkTexts'](texts);
      expect(chunks).toHaveLength(1);
      expect(chunks[0]).toEqual(['Hello', 'World']);
    });

    it('应该按数量分块', () => {
      const texts = Array.from({ length: 200 }, (_, i) => `Text ${i}`);
      const chunks = engine['chunkTexts'](texts);
      expect(chunks.length).toBeGreaterThan(1);
      expect(chunks[0].length).toBeLessThanOrEqual(128);
    });

    it('应该按字符长度分块', () => {
      const longText = 'A'.repeat(6000);
      const texts = [longText, 'Short text'];
      const chunks = engine['chunkTexts'](texts);
      expect(chunks.length).toBeGreaterThan(1);
    });
  });

  describe('API请求构建', () => {
    it('应该正确构建Google API 1请求体', () => {
      const texts = ['Hello', 'World'];
      const options = { from: 'en', to: 'zh' };
      const body = (engine as any)['buildGoogleApi1Body'](texts, options);
      
      expect(typeof body).toBe('string');
      expect(body).toContain('Hello');
      expect(body).toContain('World');
    });

    it('应该正确构建Google API 2 URL和请求体', () => {
      const texts = ['Hello', 'World'];
      const options = { from: 'en', to: 'zh' };
      const url = (engine as any)['buildGoogleApi2Url'](options, 'https://example.com');
      const body = (engine as any)['buildGoogleApi2Body'](texts, options);
      
      expect(typeof body).toBe('string');
    });

    it('应该使用默认语言', () => {
      const texts = ['Hello'];
      const options = {};
      const request = (engine as any)['buildGoogleApi1Body'](texts, options);
      const parsed = JSON.parse(request);
      
      expect(parsed[0][1]).toBe('auto'); // from
      expect(parsed[0][2]).toBe('zh');   // to
    });
  });

  describe('API响应解析', () => {
    it('应该正确解析Google API 1响应', () => {
      const mockResponse = [
        [
          [['你好', 'Hello', null, null, 1]],
          [['世界', 'World', null, null, 1]]
        ]
      ];
      
      const result = (engine as any)['parseGoogleApi1Response'](mockResponse, ['Hello', 'World']);
      expect(result).toEqual(['你好', '世界']);
    });

    it('应该处理API 1响应异常', () => {
      expect(() => {
        (engine as any)['parseGoogleApi1Response'](null, ['test']);
      }).toThrow();
    });

    it('应该补充缺失的翻译', () => {
      const mockResponse = [
        [
          [['你好', 'Hello', null, null, 1]]
        ]
      ];
      
      const result = (engine as any)['parseGoogleApi1Response'](mockResponse, ['Hello', 'World']);
      expect(result).toEqual(['你好', '']);
    });

    it('应该正确解析Google API 2响应', () => {
      const mockResponse = {
        data: {
          translations: [
            { translatedText: '你好' },
            { translatedText: '世界' }
          ]
        }
      };
      
      const result = (engine as any)['parseGoogleApi2Response'](JSON.stringify(mockResponse), ['Hello', 'World']);
      expect(result).toEqual(['你好', '世界']);
    });
  });

  describe('翻译请求', () => {
    it('应该通过background script发送请求', async () => {
      const mockResponse = {
        success: true,
        data: [
          [
            [['你好', 'Hello', null, null, 1]]
          ]
        ],
        status: 200,
        statusText: 'OK'
      };
      
      mockBrowserRuntime.sendMessage.mockResolvedValue(mockResponse);
      
      const result = await engine.translateBatch(['Hello'], { from: 'en', to: 'zh' });
      
      expect(mockBrowserRuntime.sendMessage).toHaveBeenCalledWith({
        type: 'TRANSLATE_REQUEST',
        payload: {
          url: 'https://translate.googleapis.com/translate_a/single',
          options: {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            },
            body: expect.any(String)
          }
        }
      });
      
      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['你好']);
      expect(result.engine).toBe('google');
    });

    it('应该处理background请求失败', async () => {
      mockBrowserRuntime.sendMessage.mockResolvedValue({
        success: false,
        error: 'Network error'
      });
      
      await expect(engine.translateBatch(['Hello'], {})).rejects.toThrow();
    });

    it('应该处理空文本数组', async () => {
      await expect(engine.translateBatch([], {})).rejects.toThrow(
        'Input texts cannot be empty'
      );
    });
  });

  describe('API降级策略', () => {
    it('应该优先使用API 1', async () => {
      const mockResponse = {
        success: true,
        data: [[[['你好', 'Hello', null, null, 1]]]],
        status: 200
      };
      
      mockBrowserRuntime.sendMessage.mockResolvedValue(mockResponse);
      
      await engine.translateBatch(['Hello'], { from: 'en', to: 'zh' });
      
      const call = mockBrowserRuntime.sendMessage.mock.calls[0][0];
      expect(call.payload.url).toBe('https://translate.googleapis.com/translate_a/single');
    });

    it('应该在API 1失败时尝试API 2', async () => {
      // 启用API 2
      mockConfig.apis[1].enabled = true;
      mockConfig.apis[1].url = 'https://api2.translate.com';
      engine = new GoogleTranslateEngine(mockConfig);
      
      mockBrowserRuntime.sendMessage
        .mockRejectedValueOnce(new Error('API 1 failed'))
        .mockResolvedValueOnce({
          success: true,
          data: { data: { translations: [{ translatedText: '你好' }] } },
          status: 200
        });
      
      const result = await engine.translateBatch(['Hello'], { from: 'en', to: 'zh' });
      
      expect(mockBrowserRuntime.sendMessage).toHaveBeenCalledTimes(2);
      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['你好']);
    });

    it('应该在所有API失败时抛出错误', async () => {
      mockBrowserRuntime.sendMessage.mockRejectedValue(new Error('All APIs failed'));
      
      await expect(engine.translateBatch(['Hello'], {})).rejects.toThrow(
        'All Google APIs failed'
      );
    });
  });

  describe('语言检测', () => {
    it('应该检测语言', async () => {
      const mockResponse = {
        success: true,
        data: [[[['你好', 'Hello', null, null, 1]]]],
        status: 200
      };
      
      mockBrowserRuntime.sendMessage.mockResolvedValue(mockResponse);
      
      const language = await engine.detectLanguage('Hello');
      expect(language).toBe('auto'); // 简化实现返回auto
    });

    it('应该处理空文本', async () => {
      const language = await engine.detectLanguage('');
      expect(language).toBe('auto');
    });
  });

  describe('配置更新', () => {
    it('应该更新Google配置', () => {
      const updates = { timeout: 15000 };
      engine.updateGoogleConfig(updates);
      
      const config = engine.getGoogleConfig();
      expect(config.timeout).toBe(15000);
    });
  });

  describe('错误处理', () => {
    it('应该处理网络错误', async () => {
      mockBrowserRuntime.sendMessage.mockRejectedValue(new Error('Network error'));
      
      await expect(engine.translateBatch(['Hello'], {})).rejects.toMatchObject({
        type: TranslateErrorType.NETWORK_ERROR
      });
    });

    it('应该处理API错误', async () => {
      mockBrowserRuntime.sendMessage.mockResolvedValue({
        success: false,
        error: 'API error'
      });
      
      await expect(engine.translateBatch(['Hello'], {})).rejects.toThrow();
    });
  });
});