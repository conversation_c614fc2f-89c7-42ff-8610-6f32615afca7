/**
 * 翻译引擎管理器测试
 * 测试引擎注册、选择、降级、健康检查等功能
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { TranslateEngineManager } from '../engine-manager';
import { TranslateConfigManager } from '../config';
import { TranslateEngine } from '../engines/base';
import { TranslateErrorType, TranslateOptions, TranslateResponse } from '../types';

// Access the global browser mock from setup.ts
declare const browser: any;

// Mock engine class
class MockTranslateEngine extends TranslateEngine {
  readonly name = 'mock-engine';
  readonly displayName = 'Mock Engine';
  readonly maxChunkSize = 1000;
  readonly maxBatchSize = 10;
  
  private shouldFail = false;
  private responseDelay = 0;
  
  constructor(config: any) {
    super(config);
  }
  
  setShouldFail(shouldFail: boolean) {
    this.shouldFail = shouldFail;
  }
  
  setResponseDelay(delay: number) {
    this.responseDelay = delay;
  }
  
  async translateBatch(texts: string[], options: TranslateOptions): Promise<TranslateResponse> {
    if (this.responseDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, this.responseDelay));
    }
    
    if (this.shouldFail) {
      throw new Error('Mock engine failed');
    }
    
    return {
      translations: texts.map(text => `[${this.name}] ${text}`),
      engine: this.name,
      api: 'mock-api',
      success: true,
      duration: this.responseDelay
    };
  }
}

describe('TranslateEngineManager', () => {
  let manager: TranslateEngineManager;
  let mockConfigManager: TranslateConfigManager;
  let mockEngine1: MockTranslateEngine;
  let mockEngine2: MockTranslateEngine;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();

    vi.mocked(browser.storage.local.get).mockResolvedValue({});
    vi.mocked(browser.storage.local.set).mockResolvedValue(undefined);

    mockConfigManager = new TranslateConfigManager();
    // Mock the updateEngineConfig method
    vi.spyOn(mockConfigManager, 'updateEngineConfig').mockImplementation(vi.fn());
    manager = new TranslateEngineManager(mockConfigManager);
    
    // Create mock engines
    mockEngine1 = new MockTranslateEngine({
      name: 'engine1',
      enabled: true,
      priority: 1,
      maxChunkSize: 1000,
      maxBatchSize: 10,
      timeout: 5000,
      retryCount: 2,
      apis: []
    });
    
    mockEngine2 = new MockTranslateEngine({
      name: 'engine2',
      enabled: true,
      priority: 2,
      maxChunkSize: 1000,
      maxBatchSize: 10,
      timeout: 5000,
      retryCount: 2,
      apis: []
    });
    
    // Override names for testing
    (mockEngine1 as any).name = 'engine1';
    (mockEngine2 as any).name = 'engine2';
  });

  afterEach(() => {
    vi.resetAllMocks();
    vi.useRealTimers();
    if (manager && typeof manager.destroy === 'function') {
      manager.destroy();
    }
  });

  describe('引擎注册', () => {
    it('应该注册引擎', () => {
      manager.registerEngine(mockEngine1);
      
      const engines = manager.getRegisteredEngines();
      expect(engines).toContain('engine1');
    });

    it('应该注销引擎', () => {
      manager.registerEngine(mockEngine1);
      manager.unregisterEngine('engine1');
      
      const engines = manager.getRegisteredEngines();
      expect(engines).not.toContain('engine1');
    });

    it('应该获取引擎实例', () => {
      manager.registerEngine(mockEngine1);
      
      const engine = manager.getEngine('engine1');
      expect(engine).toBe(mockEngine1);
    });

    it('应该返回null对于不存在的引擎', () => {
      const engine = manager.getEngine('nonexistent');
      expect(engine).toBeNull();
    });
  });

  describe('引擎选择', () => {
    beforeEach(() => {
      manager.registerEngine(mockEngine1);
      manager.registerEngine(mockEngine2);
    });

    it('应该获取可用引擎', () => {
      const available = manager.getAvailableEngines();
      expect(available).toContain('engine1');
      expect(available).toContain('engine2');
    });

    it('应该按优先级排序引擎', () => {
      const prioritized = manager.getEnginesByPriority();
      expect(prioritized[0]).toBe('engine1'); // 优先级1
      expect(prioritized[1]).toBe('engine2'); // 优先级2
    });
  });

  describe('翻译功能', () => {
    beforeEach(() => {
      manager.registerEngine(mockEngine1);
      manager.registerEngine(mockEngine2);
    });

    it('应该成功翻译文本', async () => {
      const result = await manager.translate(['Hello', 'World']);
      
      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['[engine1] Hello', '[engine1] World']);
      expect(result.engine).toBe('engine1');
    });

    it('应该处理空文本数组', async () => {
      await expect(manager.translate([])).rejects.toThrow(
        'Input texts cannot be empty'
      );
    });

    it('应该使用指定的引擎', async () => {
      const result = await manager.translate(['Hello'], { engine: 'engine2' });
      
      expect(result.translations).toEqual(['[engine2] Hello']);
      expect(result.engine).toBe('engine2');
    });

    it('应该在指定引擎失败时不降级', async () => {
      mockEngine2.setShouldFail(true);
      
      await expect(manager.translate(['Hello'], { engine: 'engine2' }))
        .rejects.toThrow('Translation failed with engine engine2');
    });
  });

  describe('引擎降级', () => {
    beforeEach(() => {
      manager.registerEngine(mockEngine1);
      manager.registerEngine(mockEngine2);
    });

    it('应该在引擎失败时降级', async () => {
      mockEngine1.setShouldFail(true);
      
      const result = await manager.translate(['Hello']);
      
      expect(result.success).toBe(true);
      expect(result.translations).toEqual(['[engine2] Hello']);
      expect(result.engine).toBe('engine2');
    });

    it('应该在所有引擎失败时抛出错误', async () => {
      mockEngine1.setShouldFail(true);
      mockEngine2.setShouldFail(true);
      
      await expect(manager.translate(['Hello']))
        .rejects.toThrow('All translation engines failed');
    });

    it('应该在禁用降级时不降级', async () => {
      // Mock config to disable fallback
      vi.spyOn(mockConfigManager, 'getConfig').mockReturnValue({
        ...mockConfigManager.getConfig(),
        enableFallback: false
      });
      
      mockEngine1.setShouldFail(true);
      
      await expect(manager.translate(['Hello']))
        .rejects.toThrow();
    });
  });

  describe('批量翻译', () => {
    beforeEach(() => {
      manager.registerEngine(mockEngine1);
    });

    it('应该处理批量翻译', async () => {
      const batches = [
        ['Hello', 'World'],
        ['Good', 'Morning']
      ];
      
      const results = await manager.translateBatch(batches);
      
      expect(results).toHaveLength(2);
      expect(results[0].success).toBe(true);
      expect(results[0].translations).toEqual(['[engine1] Hello', '[engine1] World']);
      expect(results[1].success).toBe(true);
      expect(results[1].translations).toEqual(['[engine1] Good', '[engine1] Morning']);
    });

    it('应该处理批量翻译中的错误', async () => {
      const batches = [
        ['Hello', 'World'],
        ['Good', 'Morning']
      ];
      
      // Make the first batch fail
      let callCount = 0;
      const originalTranslate = manager.translate.bind(manager);
      vi.spyOn(manager, 'translate').mockImplementation(async (texts, options) => {
        callCount++;
        if (callCount === 1) {
          throw new Error('First batch failed');
        }
        return originalTranslate(texts, options);
      });
      
      const results = await manager.translateBatch(batches);
      
      expect(results).toHaveLength(2);
      expect(results[0].success).toBe(false);
      expect(results[0].translations).toEqual(['', '']);
      expect(results[1].success).toBe(true);
      expect(results[1].translations).toEqual(['[engine1] Good', '[engine1] Morning']);
    });
  });

  describe('健康检查', () => {
    beforeEach(() => {
      manager.registerEngine(mockEngine1);
      manager.registerEngine(mockEngine2);
    });

    it('应该测试引擎连通性', async () => {
      const result = await manager.testEngine('engine1');
      expect(result).toBe(true);
    });

    it('应该处理引擎测试失败', async () => {
      mockEngine1.setShouldFail(true);
      
      const result = await manager.testEngine('engine1');
      expect(result).toBe(false);
    });

    it('应该测试所有引擎', async () => {
      mockEngine2.setShouldFail(true);
      
      const results = await manager.testAllEngines();
      
      expect(results.engine1).toBe(true);
      expect(results.engine2).toBe(false);
    });

    it('应该获取引擎状态', async () => {
      const status = await manager.getEnginesStatus();
      
      expect(status).toHaveProperty('engine1');
      expect(status).toHaveProperty('engine2');
      expect(status.engine1).toHaveProperty('available');
      expect(status.engine1).toHaveProperty('lastCheck');
    });
  });

  describe('统计信息', () => {
    beforeEach(() => {
      manager.registerEngine(mockEngine1);
    });

    it('应该获取引擎统计', () => {
      const stats = manager.getEnginesStats();
      expect(stats).toHaveProperty('engine1');
    });

    it('应该重置引擎统计', () => {
      manager.resetEnginesStats();
      
      const stats = manager.getEnginesStats();
      expect(stats.engine1).toBeDefined();
    });
  });

  describe('推荐引擎', () => {
    beforeEach(() => {
      manager.registerEngine(mockEngine1);
      manager.registerEngine(mockEngine2);
    });

    it('应该返回推荐引擎', () => {
      const recommended = manager.getRecommendedEngine();
      // 如果有可用引擎，应该返回其中一个；如果没有，返回null
      if (recommended !== null) {
        expect(['engine1', 'engine2']).toContain(recommended);
      } else {
        // 如果返回null，检查是否真的没有可用引擎
        const availableEngines = manager.getAvailableEngines();
        expect(availableEngines.length).toBe(0);
      }
    });

    it('应该在没有可用引擎时返回null', () => {
      manager.unregisterEngine('engine1');
      manager.unregisterEngine('engine2');
      
      const recommended = manager.getRecommendedEngine();
      expect(recommended).toBeNull();
    });
  });

  describe('配置更新', () => {
    beforeEach(() => {
      manager.registerEngine(mockEngine1);
    });

    it('应该更新引擎配置', async () => {
      const newConfig = { timeout: 10000 };
      
      await manager.updateEngineConfig('engine1', newConfig);
      
      // Verify config was updated
      expect(mockConfigManager.updateEngineConfig).toHaveBeenCalledWith('engine1', newConfig);
    });
  });

  describe('错误分类', () => {
    it('应该正确分类错误', () => {
      const timeoutError = new Error('Request timeout');
      const networkError = new Error('Network failed');
      const quotaError = new Error('Quota exceeded');
      const authError = new Error('Unauthorized access');
      const invalidError = new Error('Invalid request');
      
      expect(manager['classifyError'](timeoutError)).toBe(TranslateErrorType.TIMEOUT);
      expect(manager['classifyError'](networkError)).toBe(TranslateErrorType.NETWORK_ERROR);
      expect(manager['classifyError'](quotaError)).toBe(TranslateErrorType.QUOTA_EXCEEDED);
      expect(manager['classifyError'](authError)).toBe(TranslateErrorType.AUTHENTICATION_ERROR);
      expect(manager['classifyError'](invalidError)).toBe(TranslateErrorType.INVALID_INPUT);
    });
  });

  describe('服务销毁', () => {
    it('应该正确销毁管理器', () => {
      manager.registerEngine(mockEngine1);
      manager.destroy();
      
      const engines = manager.getRegisteredEngines();
      expect(engines).toHaveLength(0);
    });
  });
});