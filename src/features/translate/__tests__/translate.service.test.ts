/**
 * 翻译服务测试
 * 测试翻译服务的核心功能：引擎管理、降级策略、页面翻译等
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { TranslateService } from '../translate.service';
import { TranslateConfigManager } from '../config';
import { GoogleTranslateEngine } from '../engines/google';
import { TranslateErrorType } from '../types';

// Use the global browser mocking from setup.ts - don't override it

// Mock DOM
Object.defineProperty(global, 'document', {
  value: {
    documentElement: {
      getAttribute: vi.fn(),
      setAttribute: vi.fn(),
    },
    querySelectorAll: vi.fn(() => []),
    createTextNode: vi.fn(),
    createElement: vi.fn(() => ({
      appendChild: vi.fn(),
      setAttribute: vi.fn(),
      classList: { add: vi.fn() },
    })),
  },
  writable: true,
});

describe('TranslateService', () => {
  let service: TranslateService;
  let mockConfigManager: TranslateConfigManager;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock storage responses
    mockBrowserStorage.local.get.mockResolvedValue({});
    mockBrowserStorage.local.set.mockResolvedValue(undefined);
    
    mockConfigManager = new TranslateConfigManager();
    service = new TranslateService(mockConfigManager);
  });

  afterEach(() => {
    vi.resetAllMocks();
    service.destroy();
  });

  describe('服务初始化', () => {
    it('应该正确初始化服务', () => {
      expect(service).toBeDefined();
      expect(service.getConfigManager()).toBe(mockConfigManager);
    });

    it('应该注册默认引擎', () => {
      const engines = service.getAvailableEngines();
      expect(engines).toContain('google');
    });

    it('应该初始化统计信息', () => {
      const stats = service.getServiceStats();
      expect(stats.totalRequests).toBe(0);
      expect(stats.successfulRequests).toBe(0);
      expect(stats.failedRequests).toBe(0);
    });
  });

  describe('单文本翻译', () => {
    it('应该成功翻译单个文本', async () => {
      // Mock successful translation
      mockBrowserRuntime.sendMessage.mockResolvedValue({
        success: true,
        data: [[[['你好', 'Hello', null, null, 1]]]],
        status: 200
      });

      const result = await service.translateText('Hello', { from: 'en', to: 'zh' });
      
      // 新API直接返回翻译后的字符串
      expect(result).toBe('你好');
    });

    it('应该处理翻译失败', async () => {
      mockBrowserRuntime.sendMessage.mockRejectedValue(new Error('API error'));
      
      await expect(service.translateText('Hello')).rejects.toThrow();
    });

    it('应该处理空文本', async () => {
      await expect(service.translateText('')).rejects.toThrow();
    });

    it('应该更新成功统计', async () => {
      mockBrowserRuntime.sendMessage.mockResolvedValue({
        success: true,
        data: [[[['你好', 'Hello', null, null, 1]]]],
        status: 200
      });

      await service.translateText('Hello');
      
      const stats = service.getServiceStats();
      expect(stats.totalRequests).toBe(1);
      expect(stats.successfulRequests).toBe(1);
      expect(stats.failedRequests).toBe(0);
    });
  });

  describe('批量翻译', () => {
    it('应该成功翻译多个文本', async () => {
      mockBrowserRuntime.sendMessage.mockResolvedValue({
        success: true,
        data: [
          [
            [['你好', 'Hello', null, null, 1]],
            [['世界', 'World', null, null, 1]]
          ]
        ],
        status: 200
      });

      const result = await service.translateTexts(['Hello', 'World'], { from: 'en', to: 'zh' });
      
      // 新API直接返回字符串数组
      expect(result).toEqual(['你好', '世界']);
    });

    it('应该处理空数组', async () => {
      await expect(service.translateTexts([])).rejects.toThrow();
    });

    it('应该处理大量文本', async () => {
      const texts = Array.from({ length: 200 }, (_, i) => `Text ${i}`);
      
      mockBrowserRuntime.sendMessage.mockResolvedValue({
        success: true,
        data: [texts.map(text => [[text, text, null, null, 1]])],
        status: 200
      });

      const result = await service.translateTexts(texts);
      
      // 新API直接返回字符串数组
      expect(result).toHaveLength(200);
    });
  });

  describe('页面翻译', () => {
    it('应该翻译页面内容', async () => {
      // Mock DOM elements
      const mockTextNodes = [
        { textContent: 'Hello', parentNode: document.createElement('div') },
        { textContent: 'World', parentNode: document.createElement('div') }
      ];
      
      vi.spyOn(service as any, 'getTextNodes').mockReturnValue(mockTextNodes);
      
      mockBrowserRuntime.sendMessage.mockResolvedValue({
        success: true,
        data: [
          [
            [['你好', 'Hello', null, null, 1]],
            [['世界', 'World', null, null, 1]]
          ]
        ],
        status: 200
      });

      const result = await service.translatePage({ targetLanguage: 'zh' });
      
      expect(result.totalCount).toBe(2);
      expect(result.translatedCount).toBe(2);
      // translatePage方法仍然返回统计对象，不需要success字段
      expect(result.duration).toBeGreaterThanOrEqual(0);
    });

    it('应该排除指定的选择器', async () => {
      const mockTextNodes = [
        { textContent: 'Hello', parentNode: document.createElement('div') },
        { textContent: 'Code', parentNode: document.createElement('code') }
      ];
      
      vi.spyOn(service as any, 'getTextNodes').mockReturnValue(mockTextNodes);
      vi.spyOn(service as any, 'shouldExcludeNode').mockImplementation((node: any) => {
        return node.parentNode.tagName === 'CODE';
      });
      
      mockBrowserRuntime.sendMessage.mockResolvedValue({
        success: true,
        data: [[[['你好', 'Hello', null, null, 1]]]],
        status: 200
      });

      const result = await service.translatePage({ 
        targetLanguage: 'zh',
        excludeSelectors: ['code']
      });
      
      expect(result.totalCount).toBe(1);
      expect(result.translatedCount).toBe(1);
    });
  });

  describe('页面视图模式', () => {
    it('应该设置页面视图模式', () => {
      service.setPageViewMode('dual');
      
      expect(document.documentElement.setAttribute).toHaveBeenCalledWith('lu-view', 'dual');
    });

    it('应该获取页面视图模式', () => {
      (document.documentElement.getAttribute as any).mockReturnValue('trans');
      
      const mode = service.getPageViewMode();
      expect(mode).toBe('trans');
    });

    it('应该清除页面翻译', () => {
      const mockWrappers = [
        { remove: vi.fn() },
        { remove: vi.fn() }
      ];
      
      (document.querySelectorAll as any).mockReturnValue(mockWrappers);
      
      service.clearPageTranslations();
      
      expect(mockWrappers[0].remove).toHaveBeenCalled();
      expect(mockWrappers[1].remove).toHaveBeenCalled();
      expect(document.documentElement.setAttribute).toHaveBeenCalledWith('lu-view', 'origin');
    });
  });

  describe('引擎管理', () => {
    it('应该获取可用引擎', () => {
      const engines = service.getAvailableEngines();
      expect(engines).toContain('google');
    });

    it('应该注册新引擎', () => {
      const mockEngine = {
        name: 'test-engine',
        displayName: 'Test Engine',
        maxChunkSize: 1000,
        maxBatchSize: 10,
        translateBatch: vi.fn(),
      };
      
      service.registerEngine(mockEngine);
      const engines = service.getAvailableEngines();
      
      expect(engines).toContain('test-engine');
    });

    it('应该测试引擎连通性', async () => {
      const result = await service.testEngine('google');
      expect(typeof result).toBe('boolean');
    });

    it('应该获取引擎状态', async () => {
      const status = await service.getEnginesStatus();
      expect(status).toHaveProperty('google');
    });
  });

  describe('配置管理', () => {
    it('应该设置引擎优先级', async () => {
      await service.setEnginePriority(['google', 'microsoft']);
      
      const priority = service.getEnginePriority();
      expect(priority).toEqual(['google', 'microsoft']);
    });

    it('应该获取推荐引擎', () => {
      const recommended = service.getRecommendedEngine();
      expect(recommended).toBe('google');
    });
  });

  describe('统计信息', () => {
    it('应该获取服务统计', () => {
      const stats = service.getServiceStats();
      
      expect(stats).toHaveProperty('totalRequests');
      expect(stats).toHaveProperty('successfulRequests');
      expect(stats).toHaveProperty('failedRequests');
      expect(stats).toHaveProperty('averageLatency');
      expect(stats).toHaveProperty('engineUsage');
      expect(stats).toHaveProperty('errorsByType');
    });

    it('应该重置统计信息', () => {
      service.resetStats();
      
      const stats = service.getServiceStats();
      expect(stats.totalRequests).toBe(0);
      expect(stats.successfulRequests).toBe(0);
      expect(stats.failedRequests).toBe(0);
    });
  });

  describe('错误处理', () => {
    it('应该处理网络错误', async () => {
      mockBrowserRuntime.sendMessage.mockRejectedValue(new Error('Network error'));
      
      await expect(service.translateText('Hello')).rejects.toThrow();
      
      const stats = service.getServiceStats();
      expect(stats.failedRequests).toBe(1);
    });

    it('应该处理API错误', async () => {
      mockBrowserRuntime.sendMessage.mockResolvedValue({
        success: false,
        error: 'API error'
      });
      
      await expect(service.translateText('Hello')).rejects.toThrow();
    });
  });

  describe('服务销毁', () => {
    it('应该正确销毁服务', () => {
      service.destroy();
      
      // 验证清理工作
      expect(service).toBeDefined();
    });
  });
});